# PRD — Auction Flow (State = CLOSED)

**Version:** v1.0  
**Owner:** Zeeguros Product Team  
**Date:** Aug 2025  

---

## 🎯 Context
When an auction reaches the **CLOSED** state, the user must clearly understand that:
1. The auction is no longer active.  
2. The best 3 offers are automatically highlighted.  
3. Next steps are to get contacted by the winning brokers or proactively reach out.  

The UI should mutate from the **OPEN** view into a “finalized” state with frozen data and guided actions.  

---

## 📊 KPIs Section (Top Header)

- **Tu póliza actual**  
  - Display policy premium (same as in OPEN).  
- **Mejor Oferta**  
  - Display the lowest offer received.  
- **Ahorro Potencial**  
  - Display savings compared to current policy.  
- **Estado**  
  - Badge text: **“Cerrada”** .  
- **Tiempo Restante**  
  - Text: **“Finalizada”** (static).  

---

## 📦 Section: ¿Qué ocurre ahora?

This section replaces the “Subasta activa” messages from OPEN.  

- **Subasta finalizada**  
  “Tu subasta ha concluido. Ya no es posible recibir nuevas ofertas de agentes.”  

- **Ganadores de la subasta**  
  “Se han seleccionado automáticamente las tres mejores ofertas. Estos brokers son los que obtuvieron las condiciones más competitivas para ti.”  

- **Próximos pasos**  
  “Podrás ponerte en contacto con cualquiera de los tres brokers ganadores. Ellos también recibirán tus datos y se pondrán en contacto contigo en la mayor brevedad.”  

- **Ofertas archivadas**  
  “El resto de ofertas quedan guardadas en tu historial, pero solo los tres primeros puestos estarán destacados.”  

📞 **“Descubre ahora tus ofertas personalizadas ganadoras.”**  
Secondary text: *“Revisa las tres propuestas mejor posicionadas y elige con quién quieres contactar.”*  

---

## 📑 Auction Timeline (Cronología de la subasta)

- **Subasta iniciada** → ✅ green check.  
- **Ofertas recibidas** → ✅ green check (show total number).  
- **Subasta finaliza** → ✅ green check(display closing date & time).  
- **(Nuevo) Pendiente de firma de renovación.**  → ⚪ gray dot as is currently implemented for "Subasta finalizada" when auction is OPEN.  
- **(Nuevo) Contrato firmado.** → ⚪ gray dot as is currently implemented for "Subasta finalizada" when auction is OPEN.  

---

## 📋 Section: Ofertas Recibidas

- **List display:**  
  - Show only **Top 3** positions.  
  - Attach emojis 🥇 🥈 🥉 next to broker names.  
  - Broker names must be **full** (no initials).  
  - Keep insurer, premium, and date visible.  

- **Filters:**  
  - **Disabled** (Aseguradora, Prima, Fecha).  

- ** new Action button:**  
  - Button label: **“Contactar”**. it should open the same drawer but with different information, descrived in the next section.

---

## 🔍 Section: Contactar

When the user opens the “Comparar” drawer in CLOSED state:  

- **Resumen**  
  - Show brokers contact information and the quote sends by the broker, it should rehuse the .  

- **Coberturas**  
  - Keep coverage comparison intact (highlight improvements/degradations).  

- **New accordion**  
  - Title: **“Detalles del broker”**  
  - Content:  
    - Broker name (full) - using foreing key user_id retrieves the display_name
    - Broker identifier - identifier
    - Broker company - insurer_company
    - Broker phone number - using foreing key user_id retrieves the phone
    - Broker email - using foreing key user_id retrieves the email
  - Button: **Llamar** (calls broker phone number). 
  - Button: **Correo** (opens email app with broker email).

- **New accordion**  
  - Title: **“Detalles del broker”**  
  - Content:  
    - Broker name (full) - using foreing key user_id retrieves the display_name
    - Broker identifier - identifier
    - Broker company - insurer_company
    - Broker phone number - using foreing key user_id retrieves the phone
    - Broker email - using foreing key user_id retrieves the email
  - Button: **Llamar** (calls broker phone number). 
  - Button: **Correo** (opens email app with broker email).  

---

## ✅ Acceptance Criteria

1. All KPIs mutate correctly (Estado = “Cerrada”, Tiempo Restante = “Finalizada”).  
2. Section **¿Qué ocurre ahora?** updates to show closure messages in Spanish.  
3. Auction timeline marks **Subasta finaliza** with exact timestamp.  
4. Only Top 3 offers are visible with 🥇 🥈 🥉 and full broker names.  
5. Filters are disabled in CLOSED.  
6. Buttons in **Ofertas Recibidas** and **Comparar** must show **“Contactar”**.  

---
